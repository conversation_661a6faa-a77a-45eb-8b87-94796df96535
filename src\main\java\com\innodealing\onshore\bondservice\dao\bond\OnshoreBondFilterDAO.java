package com.innodealing.onshore.bondservice.dao.bond;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.NormPagingQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.UpdateQuery;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.onshore.bondmetadata.enums.*;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.bondservice.config.datasource.BondDataSourceConfig;
import com.innodealing.onshore.bondservice.config.datasource.PgBondDataSourceConfig;
import com.innodealing.onshore.bondservice.mapper.bond.OnshoreBondFilterMapper;
import com.innodealing.onshore.bondservice.mapper.bond.view.OnshoreBondFilterGroupViewMapper;
import com.innodealing.onshore.bondservice.mapper.bond.view.OnshoreBondFilterViewMapper;
import com.innodealing.onshore.bondservice.model.bo.BondBalanceStatisticsBO;
import com.innodealing.onshore.bondservice.model.dto.request.BondDetailInfoPageRequestDTO;
import com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondFilterDO;
import com.innodealing.onshore.bondservice.model.entity.bond.view.OnshoreBondFilterView;
import com.innodealing.onshore.bondservice.utils.CamelCaseUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.logging.log4j.util.Strings;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.asc;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;
import static com.innodealing.onshore.bondservice.config.constant.NumberConstant.INT_TWO;

/**
 * 技术债券筛选
 *
 * <AUTHOR>
 */
@Repository
public class OnshoreBondFilterDAO {

    @Resource
    private OnshoreBondFilterMapper onshoreBondFilterMapper;
    @Resource
    private OnshoreBondFilterViewMapper onshoreBondFilterViewMapper;
    @Resource
    private OnshoreBondFilterGroupViewMapper onshoreBondFilterGroupViewMapper;

    @Resource(name = BondDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    private static final Map<String, String> SORT_FIELD_MAP = new HashMap<>();

    static {
        SORT_FIELD_MAP.put("cbYte", "CASE WHEN cb_yte IS NOT NULL THEN cb_yte ELSE cb_ytm END");
        SORT_FIELD_MAP.put("csYte", "CASE WHEN cs_yte IS NOT NULL THEN cs_yte ELSE cs_ytm END");
    }

    /**
     * 保存债券筛选
     *
     * @param bondFilterDOs 债券筛选
     * @return 影响行数
     */
    @Transactional(transactionManager = BondDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int saveBondFilters(Collection<OnshoreBondFilterDO> bondFilterDOs) {
        if (CollectionUtils.isEmpty(bondFilterDOs)) {
            return 0;
        }
        Set<Long> bondUniCodes = bondFilterDOs.stream().map(OnshoreBondFilterDO::getBondUniCode).collect(Collectors.toSet());
        DynamicQuery<OnshoreBondFilterDO> query = DynamicQuery.createQuery(OnshoreBondFilterDO.class)
                .select(OnshoreBondFilterDO::getBondUniCode)
                .and(OnshoreBondFilterDO::getBondUniCode, o -> o.in(bondUniCodes));
        Set<Long> existedBondUniCodes = onshoreBondFilterMapper.selectByDynamicQuery(query).stream()
                .map(OnshoreBondFilterDO::getBondUniCode).collect(Collectors.toSet());
        MapperBatchAction<OnshoreBondFilterMapper> batchAction = MapperBatchAction.create(OnshoreBondFilterMapper.class, sqlSessionFactory);
        for (OnshoreBondFilterDO bondFilterDO : bondFilterDOs) {
            batchAction.addAction(mapper -> {
                if (existedBondUniCodes.contains(bondFilterDO.getBondUniCode())) {
                    UpdateQuery<OnshoreBondFilterDO> updateQuery = UpdateQuery.createQuery(OnshoreBondFilterDO.class)
                            .set(bondFilterDO, o -> o.ignore(
                                    OnshoreBondFilterDO::getId,
                                    OnshoreBondFilterDO::getCreateTime,
                                    OnshoreBondFilterDO::getUpdateTime))
                            .and(OnshoreBondFilterDO::getBondUniCode, o -> o.isEqual(bondFilterDO.getBondUniCode()));
                    mapper.updateByUpdateQuery(updateQuery);
                } else {
                    mapper.insertSelective(bondFilterDO);
                }
            });
        }
        List<BatchResult> batchResults = batchAction.doBatchActionWithResults();
        return batchResults.stream().mapToInt(x -> {
            int effectRows = Arrays.stream(x.getUpdateCounts()).sum();
            if (effectRows > 0) {
                return effectRows;
            } else {
                return x.getParameterObjects().size();
            }
        }).sum();
    }

    @Transactional(transactionManager = BondDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public void updateCsYteYtm(Collection<OnshoreBondFilterDO> bondFilterDOs) {
        MapperBatchAction<OnshoreBondFilterMapper> batchAction = MapperBatchAction.create(OnshoreBondFilterMapper.class, sqlSessionFactory);
        for (OnshoreBondFilterDO bondFilterDO : bondFilterDOs) {
            batchAction.addAction(mapper -> {
                UpdateQuery<OnshoreBondFilterDO> updateQuery = UpdateQuery.createQuery(OnshoreBondFilterDO.class)
                        .set(OnshoreBondFilterDO::getCsYte, bondFilterDO.getCsYte())
                        .set(OnshoreBondFilterDO::getCsYtm, bondFilterDO.getCsYtm())
                        .and(OnshoreBondFilterDO::getBondUniCode, o -> o.isEqual(bondFilterDO.getBondUniCode()));
                mapper.updateByUpdateQuery(updateQuery);
            });
        }
        batchAction.doBatchActionWithResults();
    }

    @Transactional(transactionManager = BondDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public void updateCbYteYtm(Collection<OnshoreBondFilterDO> bondFilterDOs) {
        MapperBatchAction<OnshoreBondFilterMapper> batchAction = MapperBatchAction.create(OnshoreBondFilterMapper.class, sqlSessionFactory);
        for (OnshoreBondFilterDO bondFilterDO : bondFilterDOs) {
            batchAction.addAction(mapper -> {
                UpdateQuery<OnshoreBondFilterDO> updateQuery = UpdateQuery.createQuery(OnshoreBondFilterDO.class)
                        .set(OnshoreBondFilterDO::getCbYte, bondFilterDO.getCbYte())
                        .set(OnshoreBondFilterDO::getCbYtm, bondFilterDO.getCbYtm())
                        .and(OnshoreBondFilterDO::getBondUniCode, o -> o.isEqual(bondFilterDO.getBondUniCode()));
                mapper.updateByUpdateQuery(updateQuery);
            });
        }
        batchAction.doBatchActionWithResults();
    }


    /**
     * 列出债券筛选
     *
     * @param startUniCode 开始uniCode
     * @param limit        限制大小
     * @return 债券筛选
     */
    public List<OnshoreBondFilterDO> listBondFilters(Long startUniCode, int limit) {
        DynamicQuery<OnshoreBondFilterDO> query = DynamicQuery.createQuery(OnshoreBondFilterDO.class)
                .and(OnshoreBondFilterDO::getBondUniCode, o -> o.greaterThan(startUniCode))
                .orderBy(OnshoreBondFilterDO::getBondUniCode, asc());
        return onshoreBondFilterMapper.selectRowBoundsByDynamicQuery(query, new RowBounds(0, limit));
    }

    /**
     * 获取债券筛选信息
     *
     * @param bondUniCode 债券编码
     * @return 债券筛选
     */
    public Optional<OnshoreBondFilterDO> getOnshoreBondFilter(Long bondUniCode) {
        if (Objects.isNull(bondUniCode)) {
            return Optional.empty();
        }
        return onshoreBondFilterMapper.selectFirstByDynamicQuery(DynamicQuery.createQuery(OnshoreBondFilterDO.class)
                .and(OnshoreBondFilterDO::getBondUniCode, o -> o.isEqual(bondUniCode)));
    }

    /**
     * 获取最大债券唯一编码
     *
     * @return 债券筛选
     */
    public Optional<OnshoreBondFilterDO> getMaxOnshoreBondFilterDO() {
        return onshoreBondFilterMapper.selectFirstByDynamicQuery(DynamicQuery.createQuery(OnshoreBondFilterDO.class)
                .orderBy(OnshoreBondFilterDO::getBondUniCode, SortDirections::desc));
    }


    public List<OnshoreBondFilterDO> listCrossMarketDedupStatusOnshoreBondFilterDOs(Long comUniCode, @Nullable Integer crossMarketDedupStatus) {
        if (Objects.isNull(comUniCode)) {
            return Collections.emptyList();
        }
        return onshoreBondFilterMapper.selectByDynamicQuery(DynamicQuery.createQuery(OnshoreBondFilterDO.class)
                .and(OnshoreBondFilterDO::getComUniCode, o -> o.isEqual(comUniCode))
                .and(Objects.nonNull(crossMarketDedupStatus), OnshoreBondFilterDO::getCrossMarketDedupStatus, isEqual(crossMarketDedupStatus))
                .and(OnshoreBondFilterDO::getDeleted, o -> o.isEqual(Deleted.NO_DELETED.getValue()))
        );
    }


    /**
     * 获取主体下跨市场债券bondUniCode
     *
     * @param comUniCode             主体编码
     * @param crossMarketDedupStatus 是否跨市场
     * @return 债券筛选
     */
    public List<Long> listCrossMarketDedupStatusBondUniCodes(Long comUniCode, @Nullable Integer crossMarketDedupStatus) {
        if (Objects.isNull(comUniCode)) {
            return Collections.emptyList();
        }
        return this.listCrossMarketDedupStatusOnshoreBondFilterDOs(comUniCode, crossMarketDedupStatus)
                .stream().map(OnshoreBondFilterDO::getBondUniCode).distinct().collect(Collectors.toList());
    }


    /**
     * 删除所有数据
     * 影响行数
     */
    @Transactional(transactionManager = PgBondDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public void deleteAll() {
        int count = 1;
        while (count > 0) {
            onshoreBondFilterMapper.deleteByDynamicQuery(DynamicQuery.createQuery(OnshoreBondFilterDO.class)
                    .last("LIMIT 10000"));
            count = onshoreBondFilterMapper.selectCountByDynamicQuery(DynamicQuery.createQuery(OnshoreBondFilterDO.class));
        }
    }

    /**
     * 获取主体下债券
     * @param comUniCode 主体编码
     * @return 债券筛选
     */
    public List<OnshoreBondFilterDO> listOnshoreBondFilterDOs(Long comUniCode, Integer outstandingStatus) {
        DynamicQuery<OnshoreBondFilterDO> and = DynamicQuery.createQuery(OnshoreBondFilterDO.class)
                .and(Objects.nonNull(comUniCode), OnshoreBondFilterDO::getComUniCode, o -> o.in(comUniCode))
                .and(Objects.nonNull(outstandingStatus), OnshoreBondFilterDO::getOutstandingStatus, o -> o.isEqual(outstandingStatus))
                .and(OnshoreBondFilterDO::getCrossMarketDedupStatus, o -> o.isEqual(CrossMarket.CROSS_MARKET.getValue()))
                .and(OnshoreBondFilterDO::getDeleted, o -> o.isEqual(Deleted.NO_DELETED.getValue()))
                .and(OnshoreBondFilterDO::getIssueStatus, v -> v.notIn(Arrays.asList(BondIssueStatusEnum.DELAY.getValue(),
                        BondIssueStatusEnum.CANCEL.getValue(), BondIssueStatusEnum.OTHER.getValue())));
        return onshoreBondFilterMapper.selectByDynamicQuery(and);

    }


    /**
     * 获取债券筛选分页信息
     * 性能太差
     * @param req 债券筛选分页请求
     * @return {@link NormPagingResult }<{@link OnshoreBondFilterDO }>
     */
    @Deprecated
    public NormPagingResult<OnshoreBondFilterDO> getOnshoreBondFilterPage(BondDetailInfoPageRequestDTO req) {
        NormPagingQuery<OnshoreBondFilterDO> query = NormPagingQuery.createQuery(
                        OnshoreBondFilterDO.class, req.getPageNum(), req.getPageSize(), false, true)
                .and(Objects.nonNull(req.getComUniCode()), OnshoreBondFilterDO::getComUniCode, v -> v.isEqual(req.getComUniCode()))
                .and(CollectionUtils.isNotEmpty(req.getRelationComUniCodes()), OnshoreBondFilterDO::getComUniCode, v -> v.in(req.getRelationComUniCodes()))
                .and(CollectionUtils.isNotEmpty(req.getAbsBondUniCodes()), OnshoreBondFilterDO::getBondUniCode, o -> o.in(req.getAbsBondUniCodes()))
                .and(CollectionUtils.isNotEmpty(req.getSecondMarkets()), OnshoreBondFilterDO::getSecondMarket, o -> o.in(req.getSecondMarkets()))
                .and(OnshoreBondFilterDO::getDeleted, o -> o.isEqual(Deleted.NO_DELETED.getValue()))
                // 天琳 2025-07-09 过滤 issue_status != 3,4,999
                .and(OnshoreBondFilterDO::getIssueStatus, v -> v.notIn(Arrays.asList(BondIssueStatusEnum.DELAY.getValue(),
                        BondIssueStatusEnum.CANCEL.getValue(), BondIssueStatusEnum.OTHER.getValue())))
                // 募集方式
                .and(CollectionUtils.isNotEmpty(req.getPublicOfferings()), OnshoreBondFilterDO::getPublicOffering, v -> v.in(req.getPublicOfferings()))
                .and(CollectionUtils.isNotEmpty(req.getBondTypes()), OnshoreBondFilterDO::getBondType, v -> v.in(req.getBondTypes()))
                // 债券状态切换
                .and(CollectionUtils.isNotEmpty(req.getBondStatus()), o -> o.
                        or(req.getBondStatus().contains(MarketCalendarLatestProTypeEnum.DURATION.getValue()), OnshoreBondFilterDO::getOutstandingStatus,
                                b -> b.isEqual(BigDecimal.ONE.intValue()))
                        .or(req.getBondStatus().contains(MarketCalendarLatestProTypeEnum.MATURITY.getValue()), OnshoreBondFilterDO::getExpired,
                                b -> b.isEqual(BigDecimal.ONE.intValue()))
                        .or(req.getBondStatus().contains(BondIssueStatusEnum.ISSUE.getValue()), OnshoreBondFilterDO::getIssueStatus,
                                b -> b.isEqual(BondIssueStatusEnum.ISSUE.getValue())))
                // 债券条款
                .and(CollectionUtils.isNotEmpty(req.getBondTerms()), o -> o.
                        // 永续 = 2
                                or(req.getBondTerms().contains(BondType.INTL_PERPETUAL_BOND.getValue()), OnshoreBondFilterDO::getEmbeddedOption,
                                b -> b.isEqual(INT_TWO))
                        // 回售
                        .or(req.getBondTerms().contains(BondType.INTL_RECOVER_SALE_BOND.getValue()), OnshoreBondFilterDO::getPutOptionStatus,
                                b -> b.isEqual(BigDecimal.ONE.intValue()))
                        // 赎回
                        .or(req.getBondTerms().contains(BondType.INTL_REDEMPTION_BOND.getValue()), OnshoreBondFilterDO::getRedeemStatus,
                                b -> b.isEqual(BigDecimal.ONE.intValue()))
                        // 票面利率选择权
                        .or(req.getBondTerms().contains(BondTermTypeEnum.COUPON_RATE_OPTION.getValue()), OnshoreBondFilterDO::getCouponAdjustableStatus,
                                b -> b.isEqual(BigDecimal.ONE.intValue())));

        // 跨市场并且没有勾选了市场筛选
        if (CollectionUtils.isEmpty(req.getSecondMarkets()) && Objects.equals(req.getCrossMarketDedupStatus(), CrossMarket.CROSS_MARKET.getValue())) {
            query.and(OnshoreBondFilterDO::getCrossMarketDedupStatus, v -> v.isEqual(CrossMarket.CROSS_MARKET.getValue()));
        }

        // 跨市场并且有勾选市场筛选 且这是ABS债券
        if (CollectionUtils.isNotEmpty(req.getAbsBondUniCodes())
                && CollectionUtils.isNotEmpty(req.getSecondMarkets())
                && Objects.equals(req.getCrossMarketDedupStatus(), CrossMarket.CROSS_MARKET.getValue())) {
            List<Long> bondUniCodes = this.listCrossMarketDedupStatusBondUniCodes(Collections.emptyList(), req.getAbsBondUniCodes(), req.getSecondMarkets());
            query.and(CollectionUtils.isNotEmpty(bondUniCodes), OnshoreBondFilterDO::getBondUniCode, v -> v.in(bondUniCodes));
        }

        // 跨市场并且有勾选市场筛选 且这是本主体或者实控人
        if ((CollectionUtils.isNotEmpty(req.getRelationComUniCodes()) || Objects.nonNull(req.getComUniCode()))
                && CollectionUtils.isNotEmpty(req.getSecondMarkets())
                && Objects.equals(req.getCrossMarketDedupStatus(), CrossMarket.CROSS_MARKET.getValue())) {
            List<Long> comUniCodes = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(req.getRelationComUniCodes())) {
                comUniCodes.addAll(req.getRelationComUniCodes());
            }
            comUniCodes.add(req.getComUniCode());
            List<Long> bondUniCodes = this.listCrossMarketDedupStatusBondUniCodes(comUniCodes, Collections.emptyList(), req.getSecondMarkets());
            query.and(CollectionUtils.isNotEmpty(bondUniCodes), OnshoreBondFilterDO::getBondUniCode, v -> v.in(bondUniCodes));
        }
        // 未到期(0)在前，已到期(1)在后
        // 未到期：有上市日的在前(0)，无上市日的在后(1)
        // 已到期：有到期日的在前(0)，无到期日的在后(1)
        // 未到期：按上市日降序
        // 已到期：按到期日降序
        // 交易所优先级：银行间>上交所>深交所>北交所>其他
        String orderSql = "ORDER BY\n" +
                "    expired,\n" +
                "    %s\n" +
                "    CASE\n" +
                "        WHEN expired = 0 THEN ISNULL(list_date)\n" +
                "        WHEN expired = 1 THEN ISNULL(maturity_date)\n" +
                "        ELSE 0\n" +
                "    END,\n" +
                "    CASE\n" +
                "        WHEN expired = 0 THEN list_date\n" +
                "        WHEN expired = 1 THEN maturity_date\n" +
                "        END DESC,\n" +
                "    FIELD(second_market, 3, 2, 1, 78, 99) ";
        // 如债券同时有到期估值和行权估值，优先用行权估值进行排序，如无行权估值，则用到期估值进行排序
        if (Objects.equals(req.getSortProperty(), "cbYte")
                || Objects.equals(req.getSortProperty(), "csYte")
                || Objects.equals(req.getSortProperty(), "cbYtm")
                || Objects.equals(req.getSortProperty(), "csYtm")) {
            String sortDirection = Objects.nonNull(req.getSortDirection()) ? req.getSortDirection().name() : "ASC";
            String childSortSql = SORT_FIELD_MAP.get((req.getSortProperty())) + " " + sortDirection + ",\n";
            orderSql = String.format(orderSql, childSortSql);
        } else if (StringUtils.isNotBlank(req.getSortProperty())) {
            String field = CamelCaseUtils.convertHumpToUnderLine(req.getSortProperty());
            String sortDirection = Objects.nonNull(req.getSortDirection()) ? req.getSortDirection().name() : "ASC";
            orderSql = String.format(orderSql, field + " " + sortDirection + ",\n");
        } else {
            orderSql = String.format(orderSql, Strings.EMPTY);
        }
        query.last(orderSql);
        return onshoreBondFilterMapper.selectByNormalPaging(query);
    }


    /**
     * 统计债券余额 (只返回一条聚合数据)
     * @param req 请求参数
     * @return 统计结果
     */
    @Deprecated
    public Optional<OnshoreBondFilterView> getOnshoreBondFilterView(BondBalanceStatisticsBO req) {
        DynamicQuery<OnshoreBondFilterView> groupedQuery = DynamicQuery.createQuery(OnshoreBondFilterView.class)
                .select(OnshoreBondFilterView::getDurationCount, OnshoreBondFilterView::getDurationTotalBondBalance,
                        OnshoreBondFilterView::getExpiredCount, OnshoreBondFilterView::getExpiredTotalBondBalance)
                .and(CollectionUtils.isNotEmpty(req.getComUniCodes()), OnshoreBondFilterDO::getComUniCode, o -> o.in(req.getComUniCodes()))
                .and(CollectionUtils.isNotEmpty(req.getBondUniCodes()), OnshoreBondFilterDO::getBondUniCode, o -> o.in(req.getBondUniCodes()))
                .and(OnshoreBondFilterDO::getDeleted, o -> o.isEqual(Deleted.NO_DELETED.getValue()))
                .and(CollectionUtils.isNotEmpty(req.getBondTypes()), OnshoreBondFilterDO::getBondType, v -> v.in(req.getBondTypes()))
                .and(CollectionUtils.isEmpty(req.getBondUniCodes()), OnshoreBondFilterDO::getCrossMarketDedupStatus, v -> v.isEqual(CrossMarket.CROSS_MARKET.getValue()))
                // 天琳 2025-07-09 过滤 issue_status != 3,4,999
                .and(OnshoreBondFilterDO::getIssueStatus, v -> v.notIn(Arrays.asList(BondIssueStatusEnum.DELAY.getValue(),
                        BondIssueStatusEnum.CANCEL.getValue(), BondIssueStatusEnum.OTHER.getValue())))
                // 募集方式
                .and(CollectionUtils.isNotEmpty(req.getPublicOfferings()), OnshoreBondFilterDO::getPublicOffering, v -> v.in(req.getPublicOfferings()))
                // 债券状态切换
                .and(CollectionUtils.isNotEmpty(req.getBondStatus()), o -> o.
                        or(req.getBondStatus().contains(MarketCalendarLatestProTypeEnum.DURATION.getValue()), OnshoreBondFilterDO::getOutstandingStatus,
                                b -> b.isEqual(BigDecimal.ONE.intValue()))
                        .or(req.getBondStatus().contains(MarketCalendarLatestProTypeEnum.MATURITY.getValue()), OnshoreBondFilterDO::getExpired,
                                b -> b.isEqual(BigDecimal.ONE.intValue()))
                        .or(req.getBondStatus().contains(BondIssueStatusEnum.ISSUE.getValue()), OnshoreBondFilterDO::getIssueStatus,
                                b -> b.isEqual(BondIssueStatusEnum.ISSUE.getValue())))
                // 债券条款
                .and(CollectionUtils.isNotEmpty(req.getBondTerms()), o -> o.
                        // 永续 = 2
                                or(req.getBondTerms().contains(BondType.INTL_PERPETUAL_BOND.getValue()), OnshoreBondFilterDO::getEmbeddedOption,
                                b -> b.isEqual(INT_TWO))
                        // 回售
                        .or(req.getBondTerms().contains(BondType.INTL_RECOVER_SALE_BOND.getValue()), OnshoreBondFilterDO::getPutOptionStatus,
                                b -> b.isEqual(BigDecimal.ONE.intValue()))
                        // 赎回
                        .or(req.getBondTerms().contains(BondType.INTL_REDEMPTION_BOND.getValue()), OnshoreBondFilterDO::getRedeemStatus,
                                b -> b.isEqual(BigDecimal.ONE.intValue()))
                        // 票面利率选择权
                        .or(req.getBondTerms().contains(BondTermTypeEnum.COUPON_RATE_OPTION.getValue()), OnshoreBondFilterDO::getCouponAdjustableStatus,
                                b -> b.isEqual(BigDecimal.ONE.intValue())));
        List<OnshoreBondFilterView> onshoreBondFilterViews = onshoreBondFilterViewMapper.selectByDynamicQuery(groupedQuery);
        AtomicInteger durationCount = new AtomicInteger();
        AtomicInteger expiredCount = new AtomicInteger();
        AtomicReference<BigDecimal> durationTotalBondBalance = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> expiredTotalBondBalance = new AtomicReference<>(BigDecimal.ZERO);
        OnshoreBondFilterView onshoreBondFilterView = new OnshoreBondFilterView();
        if (CollectionUtils.isNotEmpty(onshoreBondFilterViews)) {
            onshoreBondFilterViews.forEach(item -> {
                BigDecimalUtils.safeAdd(Arrays.asList(item.getDurationTotalBondBalance(), durationTotalBondBalance.get()))
                        .ifPresent(durationTotalBondBalance::set);
                BigDecimalUtils.safeAdd(Arrays.asList(item.getExpiredTotalBondBalance(), expiredTotalBondBalance.get()))
                        .ifPresent(expiredTotalBondBalance::set);
                durationCount.addAndGet(item.getDurationCount());
                expiredCount.addAndGet(item.getExpiredCount());
            });
            onshoreBondFilterView.setDurationTotalBondBalance(durationTotalBondBalance.get());
            onshoreBondFilterView.setExpiredTotalBondBalance(expiredTotalBondBalance.get());
            onshoreBondFilterView.setDurationCount(durationCount.get());
            onshoreBondFilterView.setExpiredCount(expiredCount.get());
        }
        return Optional.of(onshoreBondFilterView);
    }


    /**
     * 统计债券余额 (只返回一条聚合数据)
     * @param req 请求参数
     * @return 统计结果
     */
    public Optional<OnshoreBondFilterView> getOnshoreBondFilterStatistics(BondBalanceStatisticsBO req) {
        return Optional.ofNullable(onshoreBondFilterMapper.selectOnshoreBondFilterStatistics(req));
    }


    /**
     * 获取跨市场债券code
     *
     * @param comUniCodes 主体code
     * @param secondMarkets 二级市场
     * @return {@link List }<{@link Long }>
     */
    public List<Long> listCrossMarketDedupStatusBondUniCodes(@Nullable Collection<Long> comUniCodes,
                                                             @Nullable Collection<Long> bondUniCodes,
                                                             @Nullable Collection<Integer> secondMarkets) {
        if (CollectionUtils.isEmpty(comUniCodes) && CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        return onshoreBondFilterMapper.listCrossMarketDedupStatusBondUniCodes(comUniCodes, bondUniCodes, secondMarkets)
                .stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获取债券筛选分页信息 - 优化版本
     * 使用INNER JOIN替代大量IN查询，避免临时表产生
     *
     * @param req 债券筛选分页请求
     * @return {@link NormPagingResult }<{@link OnshoreBondFilterDO }>
     */
    public NormPagingResult<OnshoreBondFilterDO> getOnshoreBondFilterPageOptimized(BondDetailInfoPageRequestDTO req) {
        NormPagingResult<OnshoreBondFilterDO> result = new NormPagingResult<>();
        int pageNum = req.getPageNum() < 1 ? 1 : req.getPageNum();
        int pageSize = req.getPageSize();
        int queryPageSize = pageSize + 1;
        int offset = (pageNum - 1) * pageSize;
        List<OnshoreBondFilterDO> dataList = onshoreBondFilterMapper.selectPageOnshoreBondFilterPageOptimized(req, queryPageSize, offset);
        int totalCount = onshoreBondFilterMapper.selectCountOnshoreBondFilterPageOptimized(req);
        int pages = (int) Math.ceil((double) totalCount / pageSize);
        result.setTotal(totalCount);
        result.setPages(pages);
        boolean hasNext = dataList.size() > pageSize;
        boolean hasPre = pageNum > 1;
        result.setHasNextPage(hasNext);
        result.setHasPreviousPage(hasPre);
        if (dataList.size() > pageSize) {
            result.setList(dataList.subList(0, pageSize));
        } else {
            result.setList(dataList);
        }
        result.setPageNum(pageNum);
        result.setPageSize(req.getPageSize());
        return result;
    }
}
